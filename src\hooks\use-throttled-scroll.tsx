import { useEffect, useCallback, useRef } from 'react';

interface UseThrottledScrollOptions {
  delay?: number;
  passive?: boolean;
}

export function useThrottledScroll(
  callback: (scrollY: number) => void,
  options: UseThrottledScrollOptions = {}
) {
  const { delay = 16, passive = true } = options; // 16ms = 60fps
  const callbackRef = useRef(callback);
  const throttleRef = useRef<number | null>(null);
  const lastScrollY = useRef(0);

  // Update callback ref
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  const throttledHandler = useCallback(() => {
    const currentScrollY = window.scrollY;
    
    // Only update if scroll position changed significantly
    if (Math.abs(currentScrollY - lastScrollY.current) > 1) {
      lastScrollY.current = currentScrollY;
      callbackRef.current(currentScrollY);
    }
    
    throttleRef.current = null;
  }, []);

  const handleScroll = useCallback(() => {
    if (throttleRef.current === null) {
      throttleRef.current = requestAnimationFrame(throttledHandler);
    }
  }, [throttledHandler]);

  useEffect(() => {
    window.addEventListener('scroll', handleScroll, { passive });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (throttleRef.current) {
        cancelAnimationFrame(throttleRef.current);
      }
    };
  }, [handleScroll, passive]);
}

// Hook for optimized resize handling
export function useThrottledResize(
  callback: (width: number, height: number) => void,
  delay = 100
) {
  const callbackRef = useRef(callback);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  const handleResize = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      callbackRef.current(window.innerWidth, window.innerHeight);
    }, delay);
  }, [delay]);

  useEffect(() => {
    window.addEventListener('resize', handleResize, { passive: true });
    
    return () => {
      window.removeEventListener('resize', handleResize);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [handleResize]);
}
