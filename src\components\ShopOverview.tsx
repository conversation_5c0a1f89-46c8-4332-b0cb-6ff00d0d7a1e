import React, { useState, useRef, useEffect, lazy, Suspense } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Star, Users, Music,Crown,Gem} from 'lucide-react';
import { useIntersectionObserver } from '@/hooks/use-intersection-observer';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi,
} from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import shop1 from '../images/briller.webp';
import shop2 from '../images/lucia.webp';
import shop3 from '../images/vega.webp';
import shop4 from '../images/lumi.webp';

const shopData = [
  {
    id: 1,
    name: "BRILLAR",
    location: "甲府市, 山梨県",
    address: "東京都渋谷区道玄坂2-25-17 コクサイビル 3F",
    description: "2023年12月13日、甲府市にグランドオープン！ 山梨県甲府市に誕生した人気の大型店舗で、優雅なひとときをお届けします。 敷地面積は圧巻の100坪。接待や団体利用にも最適な、ステキな空間が広がっています！ アジアンテイストの豪華な内装と、厳選されたステキなキャストが至福の時間を演出します。上質なプライベートシーンはもちろん、接待などビジネスの場においても幅広くご利用頂けます。団体利用・接待用個室完備！ 落ち着いた雰囲気の中で、最上級のサービス・極上の空間・甘美なキャストと供に至福の時間をお過ごし下さい。",
    features: [
      { name: "VIPルーム完備", icon: Star },
      { name: "プレミアムカラオケ", icon: Music },
      { name: "専属シェフ料理", icon: Users }
    ],
    hours: "20:00 - 6:00",
    phone: "03-3461-8888",
    capacity: "50名様",
    image: shop1,
    pricing: {
      main: {
        title :"Main",
        single: "¥6,500",
        multiple: "¥5,500"
      },
      vip: {
        title :"VIP",
        single: "¥8,500",
        multiple: "¥7,500"
      },
      royalVip: {
        title :"Royal Vip",
        single: "¥30,000",
        multiple: "¥15,000"
      }
    }
  },
  {
    id: 2,
    name: "LUCIA",
    location: "渋谷区道玄坂",
    address: "東京都渋谷区道玄坂2-29-8 道玄坂センタービル 5F",
    description: "",
    features: [
      { name: "初心者歓迎", icon: Users },
      { name: "アットホーム", icon: Star },
      { name: "リーズナブル", icon: Music }
    ],
    hours: "20:00 - 5:00",
    phone: "03-3461-7777",
    capacity: "35名様",
    image: shop2,
    pricing: {
      main: {
        title :"Main",
        single: "¥5,500",
        multiple: "¥4,500"
      },
      vip: {
        title :"VIP",
        single: "¥8,000",
        multiple: "¥7,000"
      },
      drinks: {
        title :"Drinks",
        castDrink: "¥1,300",
        lujeDrink: "¥2,000"
      }
    }
  },
  {
    id: 3,
    name: "VEGA",
    location: "港区六本木",
    address: "東京都港区六本木3-15-24 ウイン六本木ビル 4F",
    description: "",
    features: [
      { name: "夜景一望", icon: Star },
      { name: "極上サービス", icon: Users },
      { name: "完全個室", icon: Music }
    ],
    hours: "21:00 - 6:00",
    phone: "03-5797-9999",
    capacity: "60名様",
    image: shop3,
    pricing: {
      usage: {
        title: "ご利用人数料金（お一人様）",
        single: "¥3,000",
        double: "¥2,500"
      },
      sweets: {
        title: "お菓子盛り合わせ",
        price: "¥1,000"
      }
    }
  },
  {
    id: 4,
    name: "LUMI",
    location: "新宿区歌舞伎町",
    address: "東京都新宿区歌舞伎町1-6-2 第2東亜会館 6F",
    description: "",
    features: [
      { name: "会員制", icon: Star },
      { name: "厳選キャスト", icon: Users },
      { name: "至高体験", icon: Music }
    ],
    hours: "21:00 - 7:00",
    phone: "03-6380-5555",
    capacity: "40名様",
    image: shop4,
    pricing: {
      unlimited: {
        title: "料金（無制限）",
        subtitle: "ご利用人数料金（お一人様）",
        pricing: {
          single: "¥10,000",
          double: "¥9,000",
          triple: "¥8,000",
          quad: "¥7,000",
          fivePlus: "¥6,000"
        }
      }
    }
  }
];

const ShopOverview = () => {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const sectionRef = useRef<HTMLElement>(null);
  const isInView = useIntersectionObserver(sectionRef, { threshold: 0.02 });
  const navigate = useNavigate();

  const plugin = useRef(
    Autoplay({ delay: 7000, stopOnInteraction: true })
  );

  const handleStoreClick = (storeId: number) => {
    // Store the source section for return navigation
    sessionStorage.setItem('brillar-return-source', 'home-shop-overview');
    sessionStorage.setItem('brillar-internal-nav', 'true');
    navigate(`/store/${storeId}`);
  };

  useEffect(() => {
    if (!api) {
      return;
    }

    setCurrent(api.selectedScrollSnap());

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap());
    });
  }, [api]);

  return (
    <section id="shop-overview" ref={sectionRef} className="relative py-20 overflow-hidden">
      {/* Simplified Background - No particles */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black" />
      
      {/* Subtle overlay for depth */}
      <div className="absolute inset-0 bg-black/20" />

      <div
        className={`max-w-7xl mx-auto px-4 md:px-8 relative z-10 transition-opacity duration-1000 ease-out ${
          isInView ? 'opacity-100' : 'opacity-0'
        }`}
      >
        {/* Section Title */}
        <div className="text-center mb-16">
          <h2 className="section-heading">店舗紹介</h2>
          <p className="text-gold-light text-lg font-sans-jp mt-4">
            全4店舗で皆様をお待ちしております
          </p>
        </div>

        <Carousel
          setApi={setApi}
          plugins={[plugin.current]}
          className="w-full"
          onMouseEnter={plugin.current.stop}
          onMouseLeave={plugin.current.reset}
        >
          <CarouselContent>
            {shopData.map((shop, index) => (
              <CarouselItem key={shop.id}>
                {/* Split Screen Layout */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                  {/* Left Side - Image */}
                  <div className="relative group">
                    <div onClick={() => handleStoreClick(shop.id)} className="block cursor-pointer">
                      <div className="aspect-[4/3] rounded-2xl overflow-hidden shadow-xl border border-gold/20 mobile-optimized">
                        <img
                          src={shop.image}
                          alt={shop.name}
                          loading="lazy"
                          decoding="async"
                          className="w-full h-full object-cover transition-opacity duration-300 hover:opacity-90"
                          style={{ 
                            contentVisibility: 'auto',
                            containIntrinsicSize: '400px 300px'
                          }}
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent" />

                        {/* Shop Name Overlay */}
                        <div className="absolute bottom-6 left-6 right-6">
                          <h3 className="text-2xl md:text-3xl font-bold text-white font-serif">
                            {shop.name}
                          </h3>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Right Side - Information Card */}
                  <div className="space-y-4">
                    <div className="relative bg-black/80 p-6 rounded-2xl border border-gold/30 shadow-xl mobile-optimized">
                      {/* Decorative Golden Border */}
                      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-gold/10 via-transparent to-gold/10 p-[1px]">
                        <div className="h-full w-full rounded-2xl bg-transparent" />
                      </div>

                      {/* Shop Title with Rating */}
                      <div className="relative mb-4">
                        <h3 className="text-2xl md:text-3xl font-bold text-gold mb-2 font-serif leading-tight">
                          {shop.name}
                        </h3>
                        
                        <div className="w-16 h-[1px] bg-gradient-to-r from-gold to-gold-light rounded-full"></div>
                      </div>
                      
                      {/* Description */}
                      <p className="text-gray-200 text-sm leading-relaxed mb-4 font-sans-jp line-clamp-3">
                        {shop.description}
                      </p>

                      {/* Restaurant Menu Pricing */}
                      <div className="mb-4">
                        <h4 className="text-gold text-sm font-semibold mb-4 font-serif">料金 システム</h4>
                        
                        {/* Special pricing for VEGA */}
                        {shop.name === "VEGA" ? (
                          <>
                            {/* Usage Pricing */}
                            <div className="mb-4">
                              <div className="bg-gradient-to-r from-gold/10 to-gold/5 rounded-xl p-4 border border-gold/30 mb-3">
                                <h5 className="text-gold font-semibold text-sm mb-3 font-serif flex items-center gap-2">
                                  <Users className="w-4 h-4" />
                                  {shop.pricing.usage.title}
                                </h5>
                                <div className="space-y-2">
                                  <div className="w-full h-px bg-gradient-to-r from-transparent via-gold/30 to-transparent"></div>
                                  <div className="flex justify-between items-center">
                                    <span className="text-gray-200 text-xs font-sans-jp">1名様 (1 person)</span>
                                    <span className="text-gold font-bold text-sm">{shop.pricing.usage.single}</span>
                                  </div>
                                  <div className="flex justify-between items-center">
                                    <span className="text-gray-200 text-xs font-sans-jp">2名様 (2 people)</span>
                                    <span className="text-gold font-bold text-sm">{shop.pricing.usage.double}</span>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Sweets Pricing */}
                            <div className="mb-4">
                              <div className="bg-gradient-to-r from-pink-500/10 to-pink-500/5 rounded-xl p-4 border border-pink-400/30">
                                <h5 className="text-pink-400 font-semibold text-sm mb-3 font-serif flex items-center gap-2">
                                  <Star className="w-4 h-4" />
                                  {shop.pricing.sweets.title}
                                </h5>
                                <div className="space-y-2">
                                  <div className="w-full h-px bg-gradient-to-r from-transparent via-pink-400/30 to-transparent"></div>
                                  <div className="flex justify-between items-center">
                                    <span className="text-gray-200 text-xs font-sans-jp">お菓子盛り合わせ</span>
                                    <span className="text-pink-400 font-bold text-sm">{shop.pricing.sweets.price}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </>
                        ) : shop.name === "LUMI" ? (
                          <>
                            {/* LUMI Unlimited Pricing */}
                            <div className="mb-4">
                              <div className="bg-gradient-to-r from-purple-500/10 to-purple-500/5 rounded-xl p-4 border border-purple-400/30">
                                <h5 className="text-purple-400 font-semibold text-sm mb-2 font-serif flex items-center gap-2">
                                  <Crown className="w-4 h-4" />
                                  {shop.pricing.unlimited.title}
                                </h5>
                                <p className="text-purple-300 text-xs mb-3 font-sans-jp">{shop.pricing.unlimited.subtitle}</p>
                                <div className="space-y-2">
                                  <div className="w-full h-px bg-gradient-to-r from-transparent via-purple-400/30 to-transparent"></div>
                                  <div className="flex justify-between items-center">
                                    <span className="text-gray-200 text-xs font-sans-jp">1名様 (1 person)</span>
                                    <span className="text-purple-400 font-bold text-sm">{shop.pricing.unlimited.pricing.single}</span>
                                  </div>
                                  <div className="flex justify-between items-center">
                                    <span className="text-gray-200 text-xs font-sans-jp">2名様 (2 people)</span>
                                    <span className="text-purple-400 font-bold text-sm">{shop.pricing.unlimited.pricing.double}</span>
                                  </div>
                                  <div className="flex justify-between items-center">
                                    <span className="text-gray-200 text-xs font-sans-jp">3名様 (3 people)</span>
                                    <span className="text-purple-400 font-bold text-sm">{shop.pricing.unlimited.pricing.triple}</span>
                                  </div>
                                  <div className="flex justify-between items-center">
                                    <span className="text-gray-200 text-xs font-sans-jp">4名様 (4 people)</span>
                                    <span className="text-purple-400 font-bold text-sm">{shop.pricing.unlimited.pricing.quad}</span>
                                  </div>
                                  <div className="flex justify-between items-center">
                                    <span className="text-gray-200 text-xs font-sans-jp">5名様以上 (5+ people)</span>
                                    <span className="text-purple-400 font-bold text-sm">{shop.pricing.unlimited.pricing.fivePlus}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </>
                        ) : (
                          <>
                            {/* Main Pricing for other shops */}
                            <div className="mb-4">
                              <div className="bg-gold/5 rounded-xl p-4 border border-gold/20 mb-3 mobile-optimized">
                                <h5 className="text-gold font-semibold text-sm mb-3 font-serif flex items-center gap-2">
                                  <Star className="w-4 h-4" />
                                  Main
                                </h5>
                                <div className="space-y-2">
                                  <div className="w-full h-px bg-gold/20"></div>
                                  <div className="flex justify-between items-center">
                                    <span className="text-gray-200 text-xs font-sans-jp">1名様 (1 person)</span>
                                    <span className="text-gold font-bold text-sm">{shop.pricing.main.single}</span>
                                  </div>
                                  <div className="flex justify-between items-center">
                                    <span className="text-gray-200 text-xs font-sans-jp">2名様以上 (2+ people)</span>
                                    <span className="text-gold font-bold text-sm">{shop.pricing.main.multiple}</span>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* VIP Pricing for other shops */}
                            <div className="mb-4">
                              <div className="bg-gold/10 rounded-xl p-4 border border-gold/30 relative overflow-hidden mobile-optimized">
                                <div className="absolute top-0 right-0 bg-gold text-black text-xs px-2 py-1 rounded-bl-lg font-bold">
                                  VIP
                                </div>
                                <h5 className="text-gold font-semibold text-sm mb-3 font-serif flex items-center gap-2">
                                  <Gem className="w-4 h-4" />
                                  VIP 
                                </h5>
                                <div className="space-y-2">
                                  <div className="w-full h-px bg-gold/30"></div>
                                  <div className="flex justify-between items-center">
                                    <span className="text-gray-200 text-xs font-sans-jp">1名様 (1 person)</span>
                                    <span className="text-gold font-bold text-sm">{shop.pricing.vip.single}</span>
                                  </div>
                                  <div className="flex justify-between items-center">
                                    <span className="text-gray-200 text-xs font-sans-jp">2名様以上 (2+ people)</span>
                                    <span className="text-gold font-bold text-sm">{shop.pricing.vip.multiple}</span>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Royal VIP Pricing - Only show if available */}
                            {shop.pricing.royalVip && (
                              <div className="mb-4">
                                <div className="bg-gradient-to-r from-gold/20 to-gold/10 rounded-xl p-4 border border-gold/40 relative overflow-hidden">
                                  <div className="absolute top-0 right-0 bg-gold text-black text-xs px-2 py-1 rounded-bl-lg font-bold">
                                    Royal VIP
                                  </div>
                                  <h5 className="text-gold font-semibold text-sm mb-3 font-serif flex items-center gap-2">
                                    <Crown className="w-4 h-4" />
                                    Royal VIP 
                                  </h5>
                                  <div className="space-y-2">
                                    <div className="w-full h-px bg-gradient-to-r from-transparent via-gold/40 to-transparent"></div>
                                    <div className="flex justify-between items-center">
                                      <span className="text-gray-200 text-xs font-sans-jp">1名様 (1 person)</span>
                                      <span className="text-gold font-bold text-sm">{shop.pricing.royalVip.single}</span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                      <span className="text-gray-200 text-xs font-sans-jp">2名様以上 (2+ people)</span>
                                      <span className="text-gold font-bold text-sm">{shop.pricing.royalVip.multiple}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}

                            {/* Drinks Pricing - Only show if available (for LUCIA) */}
                            {shop.pricing.drinks && (
                              <div className="mb-4">
                                <div className="bg-gradient-to-r from-blue-500/10 to-blue-500/5 rounded-xl p-4 border border-blue-400/30">
                                  <h5 className="text-blue-400 font-semibold text-sm mb-3 font-serif">
                                    ドリンク料金
                                  </h5>
                                  <div className="space-y-2">
                                    <div className="w-full h-px bg-gradient-to-r from-transparent via-blue-400/30 to-transparent"></div>
                                    <div className="flex justify-between items-center">
                                      <span className="text-gray-200 text-xs font-sans-jp">キャストドリンク</span>
                                      <span className="text-blue-400 font-bold text-sm">{shop.pricing.drinks.castDrink}</span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                      <span className="text-gray-200 text-xs font-sans-jp">ルジェドリンク</span>
                                      <span className="text-blue-400 font-bold text-sm">{shop.pricing.drinks.lujeDrink}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )}
                          </>
                        )}
                      </div>

                      {/* Call to Action */}
                      <div className="mt-4 pt-4 border-t border-gold/20 relative z-10">
                        <button
                          onClick={() => handleStoreClick(shop.id)}
                          className="block w-full bg-gradient-to-r from-gold to-gold-light text-black font-bold py-3 px-4 rounded-xl transition-all duration-200 hover:opacity-90 font-serif text-sm text-center mobile-optimized"
                        >
                          詳細を見る
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>

        {/* Navigation Dots */}
        <div className="flex justify-center space-x-3 mt-12">
          {shopData.map((_, index) => (
            <button
              key={index}
              onClick={() => api?.scrollTo(index)}
              className={`w-8 h-8 rounded-full transition-all duration-200 flex items-center justify-center text-sm font-bold mobile-optimized ${
                index === current
                  ? 'bg-gold text-black'
                  : 'bg-gray-600 text-white hover:bg-gold/60'
              }`}
            >
              {index + 1}
            </button>
          ))}
        </div>
      </div>
    </section>
  );
};

export default React.memo(ShopOverview);
