/* Mobile Performance Optimizations - 60fps Target for Low-End Devices */

/* GPU-accelerated transforms */
.mobile-optimized {
  will-change: auto;
  backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  contain: layout style paint;
}

/* Force hardware acceleration for critical elements */
.gpu-accelerated {
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
  will-change: transform;
}

/* Aggressive 60fps optimizations for mobile - Remove performance killers */
@media (max-width: 768px) {
  /* CRITICAL: Remove ALL backdrop blur effects - major GPU killer */
  .backdrop-blur-sm,
  .backdrop-blur-md,
  .backdrop-blur-lg,
  .backdrop-blur-xl,
  .backdrop-blur-2xl,
  .backdrop-blur-3xl,
  [class*="backdrop-blur"] {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
  }
  
  /* Only override backdrop-blur backgrounds for non-card elements */
  .backdrop-blur-sm:not([class*="card"]):not([class*="bg-gradient"]):not([class*="bg-black"]):not([class*="bg-gray"]):not([class*="bg-red"]):not([class*="TabsList"]):not(footer):not(footer *):not(section):not(section *),
  .backdrop-blur-md:not([class*="card"]):not([class*="bg-gradient"]):not([class*="bg-black"]):not([class*="bg-gray"]):not([class*="bg-red"]):not([class*="TabsList"]):not(footer):not(footer *):not(section):not(section *),
  .backdrop-blur-lg:not([class*="card"]):not([class*="bg-gradient"]):not([class*="bg-black"]):not([class*="bg-gray"]):not([class*="bg-red"]):not([class*="TabsList"]):not(footer):not(footer *):not(section):not(section *),
  [class*="backdrop-blur"]:not([class*="card"]):not([class*="bg-gradient"]):not([class*="bg-black"]):not([class*="bg-gray"]):not([class*="bg-red"]):not([class*="TabsList"]):not(footer):not(footer *):not(section):not(section *) {
    background: rgba(0, 0, 0, 0.85) !important;
  }

  /* Remove particle effects and background animations - EXCEPT footer elements */
  .particle-effect:not(footer *),
  .particles:not(footer *),
  .floating-particles:not(footer *),
  [class*="particle"]:not(footer *),
  .animated-bg:not(footer *),
  .bg-animation:not(footer *),
  [class*="bg-animate"]:not(footer *) {
    display: none !important;
    animation: none !important;
  }

  /* Hide geometric decorative elements that cause repaints - EXCEPT footer elements */
  .geometric-shape:not(footer *),
  .decorative-shape:not(footer *),
  .ornamental-border:not(footer *),
  [class*="geometric"]:not(footer *),
  [class*="decorative"]:not(footer *),
  .shape-overlay:not(footer *) {
    display: none !important;
  }

  /* Keep gold glow but simplify slightly */
  .gold-glow {
    text-shadow: 0 0 5px rgba(212, 175, 55, 0.3) !important;
    filter: none !important;
  }

  /* Reduce complex box shadows only */
  .shadow-lg,
  .shadow-xl,
  .shadow-2xl {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  }

  /* Optimize hover effects - keep design but prevent repaints */
  [class*="hover:scale"]:hover,
  [class*="hover:rotate"]:hover,
  [class*="hover:skew"]:hover,
  .group:hover [class*="group-hover:scale"],
  .group:hover [class*="group-hover:rotate"] {
    transform: none !important;
  }

  /* Remove expensive shadow effects on mobile */
  .shadow-2xl,
  .shadow-inner,
  [class*="shadow-colored"],
  .glow-effect,
  .neon-glow {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    filter: none !important;
  }

  /* Simplify card animations - only opacity changes, preserve backgrounds */
  .gallery-card,
  .cast-card,
  .pricing-card,
  .feature-card {
    transition: opacity 0.15s ease !important;
    will-change: auto !important;
    contain: layout style paint !important;
    transform: translateZ(0) !important;
  }
  
  /* Generic card selector with preserved backgrounds */
  [class*="card"]:not(.gallery-card):not(.cast-card):not(.pricing-card):not(.feature-card) {
    transition: opacity 0.15s ease !important;
    will-change: auto !important;
    contain: layout style paint !important;
    transform: translateZ(0) !important;
  }

  .gallery-card:hover,
  .cast-card:hover,
  .pricing-card:hover,
  .feature-card:hover {
    opacity: 0.9 !important;
    transform: none !important;
    animation: none !important;
  }
  
  /* Generic card hover with preserved backgrounds */
  [class*="card"]:hover:not(.gallery-card):not(.cast-card):not(.pricing-card):not(.feature-card) {
    opacity: 0.9 !important;
    transform: none !important;
    animation: none !important;
  }

  /* Disable complex animations that drop frames */
  .animate-spin,
  .animate-ping,
  .animate-pulse,
  .animate-bounce,
  .animate-royal-shimmer,
  .animate-zoom-slow,
  .animate-fade-in-slow,
  .animate-slide-up,
  .animate-float,
  .animate-glow,
  .animate-wiggle,
  .animate-heartbeat,
  .infinite-scroll,
  .parallax-effect {
    animation: none !important;
    transform: none !important;
  }
  
  /* Critical: Preserve card background gradients on mobile */
  [class*="bg-gradient-to-r"][class*="from-black"],
  [class*="bg-gradient-to-br"][class*="from-black"],
  [class*="bg-gradient-to-l"][class*="from-black"],
  [class*="bg-gradient-to-bl"][class*="from-black"] {
    /* These rules ensure gradient backgrounds are not overridden */
    background-image: inherit !important;
  }
  
  /* Target specific animation duration classes to avoid ambiguity */
  .duration-75,
  .duration-100,
  .duration-150,
  .duration-200,
  .duration-300,
  .duration-500,
  .duration-700,
  .duration-1000 {
    animation-duration: 0ms !important;
    transition-duration: 0ms !important;
  }

  /* Remove expensive CSS filters and effects */
  [class*="filter-"],
  [class*="blur-"],
  [class*="brightness-"],
  [class*="contrast-"],
  [class*="saturate-"],
  [class*="sepia-"],
  [class*="grayscale-"],
  .glass-effect,
  .frosted-glass {
    filter: none !important;
    -webkit-filter: none !important;
  }

  /* Simplify button hover effects */
  .royal-button:hover {
    transform: none !important;
    box-shadow: 0 4px 12px rgba(212, 175, 55, 0.2) !important;
  }

  /* Remove expensive filter effects on hover only */
  [class*="group-hover:brightness"]:hover,
  [class*="hover:brightness"]:hover {
    filter: none !important;
  }

  /* Optimize image transforms for 60fps */
  .group:hover img,
  [class*="hover:scale"]:hover img,
  img[class*="transform"] {
    transform: translateZ(0) !important;
    filter: none !important;
  }

  /* Remove text effects that cause repaints */
  .text-glow,
  .text-shadow-lg,
  [class*="text-shadow"],
  .glitch-effect,
  .typewriter-effect {
    text-shadow: none !important;
    animation: none !important;
  }

  /* Disable gradient animations and complex backgrounds */
  .gradient-animation,
  .moving-gradient,
  .animated-gradient {
    animation: none !important;
  }
  
  /* Don't override footer and section backgrounds */
  [class*="bg-animate"]:not(footer):not(footer *):not(section):not(section *) {
    background: rgba(0, 0, 0, 0.9) !important;
    animation: none !important;
  }

  /* Remove 3D transforms that are expensive */
  [class*="rotate-x"],
  [class*="rotate-y"],
  [class*="perspective"],
  .transform-3d,
  .flip-card {
    transform: none !important;
    perspective: none !important;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  /* Fix ambiguous duration classes */
  [class*="duration-"],
  [class*="animate-"],
  [class*="transition-"] {
    animation-duration: 0.01ms !important;
    transition-duration: 0.01ms !important;
  }
}

/* Performance-optimized card styles */
.perf-card {
  contain: layout style paint;
  will-change: auto;
  transition: opacity 0.2s ease;
  backface-visibility: hidden;
  transform: translateZ(0);
}

.perf-card:hover {
  opacity: 0.9;
}

/* Mobile-specific card background preservation */
@media (max-width: 768px) {
  /* Ensure cast cards maintain gradient backgrounds */
  .cast-card,
  [class*="cast-card"] {
    background: linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(20,20,20,0.9) 100%) !important;
  }
  
  /* Ensure notice board and store location cards maintain gradient backgrounds */
  .group.cursor-pointer[class*="bg-gradient-to-r"],
  [class*="bg-gradient-to-r"][class*="from-black"][class*="to-gray-900"] {
    background: linear-gradient(to right, rgba(0,0,0,0.8), rgba(31,41,55,0.8)) !important;
  }
  
  /* Ensure AllCast cards maintain gradient backgrounds */
  [class*="bg-gradient-to-br"][class*="from-black"][class*="to-gray-900"] {
    background: linear-gradient(135deg, rgba(0,0,0,0.8), rgba(31,41,55,0.8)) !important;
  }
  
  /* Preserve button background colors */
  [class*="bg-gold"][class*="hover:bg-gold"] {
    background-color: rgba(212, 175, 55, 0.2) !important;
  }
  
  [class*="bg-gold"][class*="hover:bg-gold"]:hover {
    background-color: rgba(212, 175, 55, 1) !important;
  }
  
  /* Ensure backdrop-blur elements with gradients keep their backgrounds */
  [class*="backdrop-blur"][class*="bg-gradient"] {
    background-image: inherit !important;
  }
}

/* Critical 60fps scroll optimizations */
.scroll-container,
.overflow-scroll,
.scrollable {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: auto; /* Remove smooth scroll for performance */
  will-change: scroll-position;
  contain: layout style paint;
}

/* Aggressive mobile scroll performance */
@media (max-width: 768px) {
  /* Force GPU acceleration on scrollable elements */
  body, html {
    transform: translateZ(0);
    backface-visibility: hidden;
  }
  
  /* Remove scroll-linked animations */
  .scroll-triggered,
  .parallax,
  .scroll-fade,
  [class*="scroll-"] {
    animation: none !important;
    transform: none !important;
  }
  
  /* Optimize intersection observer triggers */
  .fade-in-on-scroll,
  .slide-in-on-scroll,
  .zoom-in-on-scroll {
    opacity: 1 !important;
    transform: none !important;
    animation: none !important;
  }
}

/* Prevent layout thrashing */
.prevent-layout-shift {
  contain: layout style;
  transform: translateZ(0);
}

/* Mobile-specific image optimization - preserve design */
@media (max-width: 768px) {
  img {
    will-change: auto;
    backface-visibility: hidden;
    transform: translateZ(0);
  }

  /* Only disable hover scale effects on images */
  .group:hover img[class*="scale"] {
    transform: translateZ(0) !important;
  }
}

/* Performance-critical optimizations for low-end devices */
@media (max-width: 768px) and (max-device-memory: 4) {
  /* Ultra-aggressive optimizations for devices with ≤4GB RAM */
  * {
    will-change: auto !important;
    contain: layout !important;
  }
  
  /* Disable all transitions on low-end devices */
  *,
  *::before,
  *::after {
    transition: none !important;
    animation: none !important;
  }
  
  /* Force simplest rendering mode */
  img, video {
    image-rendering: pixelated !important;
    transform: none !important;
  }
}

/* Emergency 60fps mode for extremely low-end devices */
@media (max-width: 480px) and (max-device-memory: 2) {
  /* Remove all decorative elements */
  .decoration,
  .ornament,
  .visual-effect,
  [class*="effect-"],
  [class*="visual-"] {
    display: none !important;
  }
  
  /* Minimal animation mode */
  .essential-animation {
    animation-duration: 0.1s !important;
    animation-timing-function: linear !important;
  }
}

/* Optimize font rendering for mobile */
@media (max-width: 768px) {
  body, html {
    text-rendering: optimizeSpeed;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Final mobile background preservation overrides */
@media (max-width: 768px) {
  /* Override aggressive backdrop-blur background changes for specific components */
  .bg-black\/60.backdrop-blur-sm,
  .bg-black\/50.backdrop-blur-sm,
  .bg-red-600\/90.backdrop-blur-sm {
    background: inherit !important;
  }

  /* Specifically target TabsList and similar UI components */
  .bg-black\/60.backdrop-blur-sm.border.border-gold\/20 {
    background: rgba(0, 0, 0, 0.6) !important;
  }

  /* Target live event indicators and similar elements */
  .bg-red-600\/90.backdrop-blur-sm {
    background: rgba(220, 38, 38, 0.9) !important;
  }

  /* Ensure countdown timer backgrounds are preserved */
  .bg-black\/50.backdrop-blur-sm.rounded-lg {
    background: rgba(0, 0, 0, 0.5) !important;
  }

  /* CRITICAL: Preserve footer background exactly as PC version */
  footer,
  footer * {
    /* Ensure footer and all its children maintain their backgrounds */
    background-image: inherit !important;
    background: inherit !important;
  }

  /* Specifically preserve footer's main gradient background */
  footer.relative.py-20.px-4.md\:px-8.bg-black.overflow-hidden {
    background: black !important;
  }

  /* Preserve footer's base gradient layer */
  footer .absolute.inset-0.bg-gradient-to-br.from-black.via-gray-900.to-black {
    background: linear-gradient(135deg, black 0%, rgb(17, 24, 39) 50%, black 100%) !important;
  }

  /* Preserve footer's geometric pattern overlays */
  footer .absolute.inset-0.opacity-10 > div {
    background-image: inherit !important;
    opacity: inherit !important;
  }

  /* Preserve footer's texture overlay */
  footer .absolute.inset-0.opacity-5 {
    background-image: inherit !important;
    opacity: inherit !important;
    mix-blend-mode: overlay !important;
  }

  /* Ensure footer decorative corner elements are preserved */
  footer .absolute.top-8.left-8,
  footer .absolute.top-8.right-8,
  footer .absolute.bottom-8.left-8,
  footer .absolute.bottom-8.right-8 {
    display: block !important;
    border-color: rgba(212, 175, 55, 0.2) !important;
    opacity: 0.5 !important;
    width: 3rem !important;
    height: 3rem !important;
  }
}
