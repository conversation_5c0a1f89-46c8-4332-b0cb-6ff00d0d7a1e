/* Lazy Loading Performance Optimizations */

/* Content visibility for better performance */
.lazy-component {
  content-visibility: auto;
  contain-intrinsic-size: auto 200px;
}

/* Image lazy loading optimizations */
.lazy-image {
  content-visibility: auto;
  contain-intrinsic-size: 400px 300px;
  background-color: #1a1a1a;
  transition: opacity 0.3s ease-in-out;
}

.lazy-image.loading {
  opacity: 0.7;
  background-image: linear-gradient(90deg, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.lazy-image.loaded {
  opacity: 1;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Performance-critical sections */
.critical-section {
  contain: layout style paint;
  will-change: auto;
}

/* Non-critical sections that can be lazy loaded */
.non-critical-section {
  content-visibility: auto;
  contain-intrinsic-size: auto 400px;
}

/* Optimize carousel/slider performance */
.carousel-optimized {
  contain: layout style paint;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.carousel-item {
  content-visibility: auto;
  contain-intrinsic-size: auto 500px;
}

/* Gallery optimizations */
.gallery-grid {
  content-visibility: auto;
  contain-intrinsic-size: auto 800px;
}

.gallery-item {
  content-visibility: auto;
  contain-intrinsic-size: 300px 400px;
  contain: layout style paint;
}

/* Hero section optimizations */
.hero-optimized {
  contain: layout style paint;
  will-change: auto;
}

/* Footer and low-priority content */
.footer-content {
  content-visibility: auto;
  contain-intrinsic-size: auto 300px;
}

/* Loading skeleton styles */
.skeleton {
  background: linear-gradient(90deg, #1a1a1a 25%, #2a2a2a 50%, #1a1a1a 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-text {
  height: 1rem;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.skeleton-text.large {
  height: 1.5rem;
}

.skeleton-text.small {
  height: 0.75rem;
  width: 60%;
}

.skeleton-image {
  aspect-ratio: 16/9;
  border-radius: 8px;
}

.skeleton-card {
  padding: 1rem;
  border-radius: 12px;
  background-color: #1a1a1a;
}

/* Image placeholder styles */
.image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #1a1a1a;
  color: #666;
  font-size: 0.875rem;
}

/* Performance monitoring styles */
.performance-monitor {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 9999;
  pointer-events: none;
}

/* Critical resource hints */
.preload-hint {
  position: absolute;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}

/* Reduce motion for accessibility and performance */
@media (prefers-reduced-motion: reduce) {
  .lazy-image,
  .skeleton,
  .shimmer {
    animation: none !important;
    transition: none !important;
  }
}

/* Mobile-specific lazy loading optimizations */
@media (max-width: 768px) {
  .lazy-component {
    contain-intrinsic-size: auto 300px;
  }
  
  .gallery-item {
    contain-intrinsic-size: 250px 350px;
  }
  
  .carousel-item {
    contain-intrinsic-size: auto 400px;
  }
  
  /* More aggressive content visibility on mobile */
  .mobile-lazy {
    content-visibility: auto;
    contain-intrinsic-size: auto 200px;
  }
}

/* Low-end device optimizations */
@media (max-width: 480px) and (max-device-memory: 2) {
  .lazy-image.loading {
    animation: none;
    background: #1a1a1a;
  }
  
  .skeleton {
    animation: none;
    background: #1a1a1a;
  }
}
