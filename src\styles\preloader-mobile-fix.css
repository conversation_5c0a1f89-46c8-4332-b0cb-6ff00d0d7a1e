/* 
 * Critical CSS overrides for Preloader mobile compatibility
 * This file ensures the preloader works on all mobile devices
 * by overriding aggressive mobile performance optimizations
 */

/* CRITICAL: Override mobile performance CSS specifically for preloader */
.preloader-container,
.preloader-container *,
.preloader-container *::before,
.preloader-container *::after {
  animation-duration: unset !important;
  transition-duration: unset !important;
  animation: unset !important;
  transition: unset !important;
  filter: unset !important;
  transform: unset !important;
}

/* Force enable GPU acceleration for smooth 60fps on mobile */
.preloader-logo-container {
  will-change: transform, filter !important;
  backface-visibility: hidden !important;
  transform: translateZ(0) !important;
  contain: layout style paint !important;
}

.preloader-loading-dot {
  will-change: transform, opacity !important;
  backface-visibility: hidden !important;
  transform: translateZ(0) !important;
}

/* Ensure preloader works on low-end devices (≤4GB RAM) */
@media (max-width: 768px) and (max-device-memory: 4) {
  .preloader-container,
  .preloader-container * {
    animation: unset !important;
    transition: unset !important;
    will-change: auto !important;
  }
  
  /* Re-enable essential preloader animations */
  .preloader-container .preloader-logo-container {
    will-change: transform, filter !important;
  }
  
  .preloader-container .preloader-loading-dot {
    will-change: transform, opacity !important;
  }
}

/* Ensure preloader works on extremely low-end devices (≤2GB RAM) */
@media (max-width: 480px) and (max-device-memory: 2) {
  .preloader-container,
  .preloader-container * {
    animation: unset !important;
    transition: unset !important;
  }
  
  /* Re-enable essential preloader animations */
  .preloader-container .preloader-logo-container {
    will-change: transform, filter !important;
  }
  
  .preloader-container .preloader-loading-dot {
    will-change: transform, opacity !important;
  }
}

/* Override reduced motion for preloader - it's essential for UX */
@media (prefers-reduced-motion: reduce) {
  .preloader-container,
  .preloader-container *,
  .preloader-container *::before,
  .preloader-container *::after {
    animation-duration: unset !important;
    animation-iteration-count: unset !important;
    transition-duration: unset !important;
  }
}

/* Override mobile performance CSS that disables duration classes */
@media (max-width: 768px) {
  .preloader-container .duration-75,
  .preloader-container .duration-100,
  .preloader-container .duration-150,
  .preloader-container .duration-200,
  .preloader-container .duration-300,
  .preloader-container .duration-500,
  .preloader-container .duration-700,
  .preloader-container .duration-1000 {
    animation-duration: unset !important;
    transition-duration: unset !important;
  }
}

/* Override filter removal for preloader glow effects */
@media (max-width: 768px) {
  .preloader-container [class*="filter-"],
  .preloader-container [class*="blur-"],
  .preloader-container [class*="brightness-"],
  .preloader-container [class*="contrast-"],
  .preloader-container [class*="saturate-"],
  .preloader-container [class*="sepia-"],
  .preloader-container [class*="grayscale-"],
  .preloader-container .glass-effect,
  .preloader-container .frosted-glass {
    filter: unset !important;
    -webkit-filter: unset !important;
  }
}

/* Ensure preloader shadow effects work on mobile */
@media (max-width: 768px) {
  .preloader-container .shadow-2xl,
  .preloader-container .shadow-inner,
  .preloader-container [class*="shadow-colored"],
  .preloader-container .glow-effect,
  .preloader-container .neon-glow {
    box-shadow: unset !important;
    filter: unset !important;
  }
}

/* Mobile-specific optimizations for preloader performance */
@media (max-width: 768px) {
  .preloader-container {
    /* Force hardware acceleration */
    transform: translateZ(0) !important;
    backface-visibility: hidden !important;
    
    /* Optimize rendering */
    contain: layout style paint !important;
    isolation: isolate !important;
    
    /* Prevent layout shifts */
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
  }
  
  .preloader-logo-container img {
    /* Optimize image rendering */
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
    
    /* Force GPU acceleration */
    transform: translateZ(0) !important;
    backface-visibility: hidden !important;
  }
}

/* Fallback for browsers that don't support device-memory */
@media (max-width: 768px) and (max-resolution: 1.5dppx) {
  /* Likely low-end device based on screen resolution */
  .preloader-container {
    /* Simplified animations for low-end devices */
    animation-timing-function: linear !important;
  }
  
  .preloader-container * {
    animation-timing-function: linear !important;
    transition-timing-function: linear !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .preloader-container {
    background: #000000 !important;
  }
  
  .preloader-container .text-gold {
    color: #FFFFFF !important;
    text-shadow: none !important;
  }
  
  .preloader-container .bg-gold {
    background-color: #FFFFFF !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .preloader-container {
    background: #000000 !important;
  }
}

/* Light mode support */
@media (prefers-color-scheme: light) {
  .preloader-container {
    background: #1a1a1a !important;
  }
}

/* Ensure preloader is always visible and functional */
.preloader-container {
  /* Critical positioning */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;
  
  /* Prevent scrolling */
  overflow: hidden !important;
  
  /* Ensure visibility */
  visibility: visible !important;
  opacity: 1 !important;
  
  /* Performance optimizations */
  contain: layout style paint !important;
  isolation: isolate !important;
}

/* Prevent any interference from other CSS */
.preloader-container * {
  box-sizing: border-box !important;
}
