import React, { useEffect, useState, useCallback, useMemo, useRef } from "react";
import { cn } from "@/lib/utils";
import { useMobileOptimized } from "@/hooks/use-mobile-optimized";

interface BackgroundSlideshowProps {
  images: string[];
  interval?: number;
  className?: string;
  enableAnimations?: boolean;
}

const BackgroundSlideshow: React.FC<BackgroundSlideshowProps> = ({
  images,
  interval = 5000,
  className,
  enableAnimations = true
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [loadedImages, setLoadedImages] = useState<Set<number>>(new Set([0]));
  const { isMobile, isLowEndDevice, reducedMotion } = useMobileOptimized();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const preloadedImages = useRef<HTMLImageElement[]>([]);

  // Aggressive preload strategy for better performance
  const preloadImages = useCallback(() => {
    // Only preload first 3 images on low-end devices
    const imagesToPreload = isLowEndDevice ? Math.min(3, images.length) : images.length;
    
    images.slice(0, imagesToPreload).forEach((src, index) => {
      if (!preloadedImages.current[index]) {
        const img = new Image();
        img.onload = () => {
          setLoadedImages(prev => new Set([...prev, index]));
        };
        img.src = src;
        preloadedImages.current[index] = img;
      }
    });
  }, [images, isLowEndDevice]);

  useEffect(() => {
    preloadImages();
  }, [preloadImages]);

  // Optimized slideshow logic
  const startSlideshow = useCallback(() => {
    if (images.length <= 1 || reducedMotion) return;

    // Slower interval on mobile to reduce CPU usage
    const actualInterval = isMobile ? interval * 1.5 : interval;

    intervalRef.current = setInterval(() => {
      setCurrentImageIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % images.length;
        
        // Preload next image on demand for low-end devices
        if (isLowEndDevice && !loadedImages.has(nextIndex)) {
          const img = new Image();
          img.onload = () => setLoadedImages(prev => new Set([...prev, nextIndex]));
          img.src = images[nextIndex];
        }
        
        return nextIndex;
      });
    }, actualInterval);
  }, [images, interval, isMobile, reducedMotion, isLowEndDevice, loadedImages]);

  useEffect(() => {
    startSlideshow();
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [startSlideshow]);

  // Memoized styles for performance
  const containerStyles = useMemo(() => ({
    willChange: isMobile || reducedMotion ? 'auto' : 'opacity',
    backfaceVisibility: 'hidden' as const,
    transform: 'translateZ(0)' // Force hardware acceleration
  }), [isMobile, reducedMotion]);

  if (images.length === 0) return null;

  return (
    <div
      className={cn("absolute inset-0 overflow-hidden", className)}
      style={containerStyles}
    >
      {images.map((image, index) => {
        const isActive = index === currentImageIndex;
        const isLoaded = loadedImages.has(index);

        return (
          <div
            key={index}
            className={cn(
              "absolute inset-0 bg-cover bg-center",
              // Simplified transitions for mobile
              isMobile || reducedMotion
                ? "transition-opacity duration-300"
                : "transition-opacity duration-1000",
              // Only apply zoom animation on desktop and if enabled
              !isMobile && !reducedMotion && enableAnimations && "animate-zoom-slow",
              isActive ? "opacity-100 z-10" : "opacity-0 z-0"
            )}
            style={{
              backgroundImage: isLoaded ? `url(${image})` : 'none',
              transform: isMobile ? 'translateZ(0)' : undefined,
              willChange: isActive && !isMobile && !reducedMotion ? 'transform' : 'auto',
              contain: 'strict' // Contain layout, style, paint, and size
            }}
          />
        );
      })}
      
      {/* Simplified gradient overlay for mobile */}
      <div 
        className={cn(
          "absolute inset-0 z-20",
          isMobile 
            ? "bg-black/40" 
            : "bg-hero-gradient"
        )} 
      />
    </div>
  );
};

export default BackgroundSlideshow;
