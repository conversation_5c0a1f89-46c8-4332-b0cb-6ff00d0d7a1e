import React, { useState, useRef, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useLanguage } from "@/hooks/use-language";
import { useIntersectionObserver } from "@/hooks/use-intersection-observer";
import { But<PERSON> } from "@/components/ui/button";
import MemoryDetailHeader from "@/components/MemoryDetailHeader";
import GalleryGrid from "@/components/GalleryGrid";
import ImageModal from "@/components/ImageModal";
import Footer from "@/components/Footer";
import { getMemoryById } from "@/lib/memories";

const MemoryDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { language } = useLanguage();
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useIntersectionObserver(ref, { threshold: 0.1 });

  // State for image modal
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  // Get memory data
  const memory = getMemoryById(id);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [id]);

  // Handle case where memory is not found
  if (!memory) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center px-4">
        <div className="text-center space-y-8 max-w-2xl">
          <div className="space-y-4">
            <h1 className="text-4xl md:text-6xl font-serif text-gold gold-glow">
              {language === 'en' ? 'Memory Not Found' : 'メモリが見つかりません'}
            </h1>
            <p className="text-gold-light text-lg">
              {language === 'en' 
                ? 'The memory you are looking for does not exist or may have been removed.' 
                : 'お探しのメモリは存在しないか、削除された可能性があります。'
              }
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              onClick={() => navigate(-1)}
              variant="outline"
              className="border-gold/30 text-gold hover:bg-gold/10 hover:border-gold/50 px-8 py-3 rounded-full transition-all duration-300"
            >
              {language === 'en' ? 'Go Back' : '戻る'}
            </Button>
            <Button
              onClick={() => navigate('/')}
              className="royal-button text-black font-semibold px-8 py-3 rounded-full hover:scale-105 transition-all duration-300"
            >
              {language === 'en' ? 'Back to Home' : 'ホームに戻る'}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const handleImageClick = (imageUrl: string, index: number) => {
    setSelectedImageIndex(index);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/50 to-black" />
      <div 
        className="absolute top-0 left-0 w-full h-full opacity-5"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.1'%3E%3Cpath d='M30 30l15-15v30l-15-15zm-15 0l-15-15v30l15-15z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} 
      />

      <div 
        ref={ref}
        className={`relative z-10 reveal ${isInView ? 'active' : ''}`}
      >
        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 md:px-8 py-12 space-y-20">
          {/* Memory Header */}
          <MemoryDetailHeader memory={memory} />

          {/* Event Gallery Section */}
          <div className="space-y-12">
            {/* Gallery Title */}
            <div className="text-center">
              <h2 className="text-3xl md:text-4xl font-serif text-gold gold-glow">
                {language === 'en' ? 'Event Gallery' : 'イベントギャラリー'}
              </h2>
              <div className="w-24 h-0.5 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto mt-4"></div>
            </div>

            {/* Gallery Grid */}
            <div className="max-w-6xl mx-auto">
              <GalleryGrid
                images={memory.gallery}
                eventName={language === 'en' ? memory.nameEn : memory.name}
                onImageClick={handleImageClick}
              />
            </div>
          </div>

          {/* Related Memories Section - Future Enhancement */}
          <div className="text-center py-16">
            <div className="w-32 h-0.5 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto mb-8"></div>
            <h3 className="text-2xl font-serif text-gold mb-4 gold-glow">
              {language === 'en' ? 'More Memories' : 'その他の思い出'}
            </h3>
            <p className="text-gold-light/60 text-lg">
              {language === 'en' 
                ? 'Discover more precious moments from our events...' 
                : 'イベントからの貴重な瞬間をもっと発見してください...'
              }
            </p>
            <Button
              onClick={() => navigate('/', { state: { scrollTo: 'events-memories' } })}
              variant="outline"
              className="mt-6 border-gold/30 text-gold hover:bg-gold/10 hover:border-gold/50 px-8 py-3 rounded-full transition-all duration-300"
            >
              {language === 'en' ? 'View All Memories' : 'すべての思い出を見る'}
            </Button>
          </div>
        </div>
      </div>

      {/* Footer */}
      <Footer />

      {/* Image Modal */}
      <ImageModal
        isOpen={isModalOpen}
        onClose={closeModal}
        images={memory.gallery}
        currentIndex={selectedImageIndex}
        eventName={language === 'en' ? memory.nameEn : memory.name}
      />
    </div>
  );
};

export default MemoryDetailPage;
