import React, { useState, useEffect } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useLanguage } from "@/hooks/use-language";
import { X, ChevronLeft, ChevronRight, Download, Maximize2 } from "lucide-react";

interface ImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  images: string[];
  currentIndex: number;
  eventName: string;
}

const ImageModal: React.FC<ImageModalProps> = ({ 
  isOpen, 
  onClose, 
  images, 
  currentIndex, 
  eventName 
}) => {
  const { language } = useLanguage();
  const [activeIndex, setActiveIndex] = useState(currentIndex);

  // Update active index when currentIndex changes
  useEffect(() => {
    setActiveIndex(currentIndex);
  }, [currentIndex]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isOpen) return;
      
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          goToPrevious();
          break;
        case 'ArrowRight':
          event.preventDefault();
          goToNext();
          break;
        case 'Escape':
          event.preventDefault();
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, activeIndex, images.length]);

  const goToPrevious = () => {
    setActiveIndex((prev) => (prev > 0 ? prev - 1 : images.length - 1));
  };

  const goToNext = () => {
    setActiveIndex((prev) => (prev < images.length - 1 ? prev + 1 : 0));
  };

  const currentImage = images[activeIndex];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[95vh] bg-black/95 border-gold/30 backdrop-blur-md p-0 overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 pb-2 border-b border-gold/20">
          <h2 className="text-gold text-xl font-serif gold-glow">
            {language === 'en' 
              ? `${eventName} - Photo ${activeIndex + 1} of ${images.length}`
              : `${eventName} - 写真 ${activeIndex + 1} / ${images.length}`
            }
          </h2>
          <Button
            variant="outline"
            size="icon"
            onClick={onClose}
            className="bg-black/70 border-gold/30 text-gold hover:bg-red-500/20 hover:border-red-500/50 hover:text-red-400 transition-colors duration-300"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Main Image Section */}
        <div className="relative flex-1 flex items-center justify-center p-6 pt-4">
          {/* Main Image */}
          <div className="relative max-w-full max-h-[65vh] overflow-hidden rounded-xl border border-gold/20">
            <img
              src={currentImage}
              alt={`${eventName} - Photo ${activeIndex + 1}`}
              className="max-w-full max-h-full object-contain"
            />
            
            {/* Image overlay with decorative corners */}
            <div className="absolute top-3 left-3 w-6 h-6 border-l-2 border-t-2 border-gold/60"></div>
            <div className="absolute top-3 right-3 w-6 h-6 border-r-2 border-t-2 border-gold/60"></div>
            <div className="absolute bottom-3 left-3 w-6 h-6 border-l-2 border-b-2 border-gold/60"></div>
            <div className="absolute bottom-3 right-3 w-6 h-6 border-r-2 border-b-2 border-gold/60"></div>
          </div>

          {/* Navigation Buttons - Only show if multiple images */}
          {images.length > 1 && (
            <>
              {/* Previous Button */}
              <Button
                variant="outline"
                size="icon"
                onClick={goToPrevious}
                className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/80 border-gold/30 text-gold hover:bg-gold/10 hover:border-gold/50 w-12 h-12 rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110"
              >
                <ChevronLeft className="w-6 h-6" />
              </Button>

              {/* Next Button */}
              <Button
                variant="outline"
                size="icon"
                onClick={goToNext}
                className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/80 border-gold/30 text-gold hover:bg-gold/10 hover:border-gold/50 w-12 h-12 rounded-full backdrop-blur-sm transition-all duration-300 hover:scale-110"
              >
                <ChevronRight className="w-6 h-6" />
              </Button>
            </>
          )}
        </div>

        {/* Thumbnail Navigation - Only show if multiple images */}
        {images.length > 1 && (
          <div className="px-6 py-4 border-t border-gold/20">
            <div className="flex gap-3 justify-center overflow-x-auto max-w-full pb-2">
              {images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setActiveIndex(index)}
                  className={`relative flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                    index === activeIndex 
                      ? 'border-gold scale-110 shadow-[0_0_20px_rgba(212,175,55,0.5)]' 
                      : 'border-gold/30 hover:border-gold/60 hover:scale-105'
                  }`}
                >
                  <img
                    src={image}
                    alt={`Thumbnail ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                  {index === activeIndex && (
                    <div className="absolute inset-0 bg-gold/20 backdrop-blur-sm" />
                  )}
                  <div className="absolute bottom-1 right-1 bg-black/70 text-gold text-xs px-1 rounded">
                    {index + 1}
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Footer with keyboard shortcuts */}
        <div className="px-6 pb-4 text-center border-t border-gold/10">
          <p className="text-gold-light/60 text-sm flex items-center justify-center gap-4">
            <span>
              {language === 'en' 
                ? 'Use ← → keys to navigate'
                : '← → キーでナビゲート'
              }
            </span>
            <span>•</span>
            <span>
              {language === 'en' 
                ? 'Press ESC to close'
                : 'ESCキーで閉じる'
              }
            </span>
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ImageModal;
