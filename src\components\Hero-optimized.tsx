import React, { useState, useEffect, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useMobileOptimized } from "@/hooks/use-mobile-optimized";
import hero1 from "../images/hero1.webp";
import hero2 from "../images/hero2.webp";
import hero3 from "../images/hero3.webp";
import hero4 from "../images/hero4.webp";

const Hero: React.FC = () => {
  const { isMobile, isLowEndDevice, reducedMotion, getAnimationProps } = useMobileOptimized();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [startSlider, setStartSlider] = useState(false);

  // Optimized slides array - fewer slides for low-end devices
  const slides = useMemo(() => {
    const allSlides = [
      {
        image: hero1,
        title: "BRILLAR",
        subtitle: "Luxury Experience Awaits"
      },
      {
        image: hero2,
        title: "ELEGANCE",
        subtitle: "Sophistication Redefined"
      },
      {
        image: hero3,
        title: "LUXURY",
        subtitle: "Premium Entertainment"
      },
      {
        image: hero4,
        title: "EXCELLENCE",
        subtitle: "Unmatched Quality"
      }
    ];
    
    // Use fewer slides on low-end devices
    return isLowEndDevice ? allSlides.slice(0, 2) : allSlides;
  }, [isLowEndDevice]);

  // Optimized slider logic
  useEffect(() => {
    const timer = setTimeout(() => {
      setStartSlider(true);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (!startSlider || reducedMotion) return;

    setCurrentSlide(0);
    let interval: NodeJS.Timeout;

    const startDelay = setTimeout(() => {
      interval = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % slides.length);
      }, isMobile ? 6000 : 5000); // Slower on mobile
    }, 1000);

    return () => {
      clearTimeout(startDelay);
      if (interval) clearInterval(interval);
    };
  }, [slides.length, startSlider, isMobile, reducedMotion]);

  // Optimized animation variants
  const imageVariants = useMemo(() => {
    if (reducedMotion || isMobile) {
      return {
        initial: { opacity: 0 },
        animate: { opacity: 1 },
        exit: { opacity: 0 },
        transition: { duration: 0.3 }
      };
    }
    
    return {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 },
      transition: { duration: 0.8 }
    };
  }, [reducedMotion, isMobile]);

  const contentVariants = useMemo(() => {
    if (reducedMotion || isMobile) {
      return {
        initial: { opacity: 0 },
        animate: { opacity: 1 },
        exit: { opacity: 0 },
        transition: { duration: 0.2 }
      };
    }
    
    return {
      initial: { opacity: 0, y: 30 },
      animate: { opacity: 1, y: 0 },
      exit: { opacity: 0, y: -30 },
      transition: { duration: 0.6, delay: 0.2 }
    };
  }, [reducedMotion, isMobile]);

  return (
    <section 
      id="hero" 
      className="relative h-screen w-full overflow-hidden bg-black !p-0 !m-0"
      style={{ contain: 'layout style paint' }}
    >
      {/* Background Slider */}
      <div className="absolute inset-0">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentSlide}
            className="absolute inset-0 w-full h-full"
            initial={imageVariants.initial}
            animate={imageVariants.animate}
            exit={imageVariants.exit}
            transition={imageVariants.transition}
          >
            {/* Background Image with Optimized Animation */}
            <motion.div
              className="absolute inset-0 w-full h-full bg-cover bg-center"
              style={{
                backgroundImage: `url(${slides[currentSlide].image})`,
                backgroundPosition: 'center center',
                backgroundRepeat: 'no-repeat',
                willChange: isMobile ? 'auto' : 'transform',
                transform: 'translateZ(0)' // Force hardware acceleration
              }}
              initial={{ scale: isMobile ? 1 : 1.02 }}
              animate={{ scale: 1 }}
              transition={{ 
                duration: isMobile ? 0 : 8, 
                ease: "easeOut" 
              }}
            />
            
            {/* Simplified Gradient Overlay for mobile */}
            <div className={`absolute inset-0 ${
              isMobile 
                ? 'bg-gradient-to-b from-black/40 to-black/70'
                : 'bg-gradient-to-b from-black/40 via-black/20 to-black/70'
            }`} />
            {!isMobile && (
              <div className="absolute inset-0 bg-gradient-to-r from-black/30 via-transparent to-black/30" />
            )}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Hero Content */}
      <div className="relative z-20 flex flex-col items-center justify-center h-full px-4 text-center">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentSlide}
            className="max-w-4xl mx-auto"
            initial={contentVariants.initial}
            animate={contentVariants.animate}
            exit={contentVariants.exit}
            transition={contentVariants.transition}
          >
            {/* Main Title */}
            <motion.h1 
              className="font-serif text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-thin mb-6 tracking-wider relative"
              {...getAnimationProps({
                initial: { opacity: 0 },
                animate: { opacity: 1 },
                transition: { duration: 0.8, delay: 0.5 }
              })}
              style={{
                background: 'linear-gradient(45deg, #D4AF37, #FFD700, #D4AF37)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                filter: isMobile ? 'none' : 'drop-shadow(0 0 20px rgba(212, 175, 55, 0.5))',
                willChange: 'auto'
              }}
            >
              {slides[currentSlide].title}
            </motion.h1>
            
            {/* Subtitle */}
            <motion.p 
              className="font-light text-xl md:text-2xl lg:text-3xl text-white/90 mb-12 tracking-wide"
              {...getAnimationProps({
                initial: { opacity: 0 },
                animate: { opacity: 1 },
                transition: { duration: 0.6, delay: 0.7 }
              })}
            >
              {slides[currentSlide].subtitle}
            </motion.p>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Slide Indicators - Hidden on mobile for performance */}
      {!isMobile && (
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30">
          <div className="flex space-x-3">
            {slides.map((_, index) => (
              <motion.button
                key={index}
                className={`w-3 h-3 rounded-full border transition-all duration-300 ${
                  currentSlide === index 
                    ? 'bg-[#D4AF37] border-[#D4AF37] shadow-lg shadow-[#D4AF37]/50' 
                    : 'bg-transparent border-white/50 hover:border-white/80'
                }`}
                onClick={() => setCurrentSlide(index)}
                whileHover={{ scale: 1.2 }}
                whileTap={{ scale: 0.9 }}
                initial={{ opacity: 0 }}
                animate={{ opacity: startSlider ? 1 : 0 }}
                transition={{ duration: 0.3, delay: startSlider ? 0.5 + index * 0.1 : 0 }}
              />
            ))}
          </div>
        </div>
      )}
    </section>
  );
};

export default Hero;
