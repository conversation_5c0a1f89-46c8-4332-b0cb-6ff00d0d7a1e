
import React, { useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { useLanguage } from "@/hooks/use-language";
import { useIntersectionObserver } from "@/hooks/use-intersection-observer";
import { Button } from "@/components/ui/button";
import { getAllMemories } from "@/lib/memories";

// Sample past events data
const pastEvents = getAllMemories();

interface EventsMemoriesProps {
  showAll?: boolean;
}

const EventsMemories: React.FC<EventsMemoriesProps> = ({ showAll = false }) => {
  const { language } = useLanguage();
  const navigate = useNavigate();
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useIntersectionObserver(ref, { threshold: 0.02 });
  const [visibleCount, setVisibleCount] = useState(showAll ? pastEvents.length : 4);

  const handleEventClick = (event: typeof pastEvents[0]) => {
    // Set return source for intelligent navigation
    sessionStorage.setItem('brillar-return-source', 'home-events-memories');
    // Store the specific event ID to scroll back to this exact card
    sessionStorage.setItem('brillar-return-event-id', event.id.toString());
    navigate(`/memories/${event.id}`);
  };

  const handleLoadMore = () => {
    setVisibleCount(prev => Math.min(prev + 4, pastEvents.length));
  };

  const displayedEvents = pastEvents.slice(0, visibleCount);

  return (
    <>
      <section id="past-events-memories" className="py-24 px-4 md:px-8 relative overflow-hidden">
        {/* Royal background elements */}
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/50 to-black" />
        <div className="absolute top-0 left-0 w-full h-full opacity-5"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4AF37' fill-opacity='0.1'%3E%3Cpath d='M30 30l15-15v30l-15-15zm-15 0l-15-15v30l15-15z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }} />

        <div
          ref={ref}
          className={`max-w-7xl mx-auto reveal relative z-10 ${isInView ? 'active' : ''}`}
        >
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="section-heading">
              {language === 'en' ? 'Events & Memories' : '過去のイベント & 思い出'}
            </h2>

            {/* Shop History Description */}
            <div className="max-w-4xl mx-auto mt-8 mb-16">
              <p className="text-gold-light text-lg leading-relaxed mb-6">
                {language === 'en'
                  ? 'Since our establishment, BRILLAR has been creating unforgettable moments and exclusive experiences. Our legacy spans years of hosting the most prestigious events in the city, where luxury meets entertainment. Each celebration becomes a cherished memory, crafted with meticulous attention to detail and an unwavering commitment to excellence.'
                  : 'BRILLARは設立以来、忘れられない瞬間と特別な体験を創造し続けています。私たちの歴史は、ラグジュアリーとエンターテイメントが融合する、街で最も権威あるイベントを主催してきた年月にわたります。それぞれのお祝いは、細部への細心の注意と卓越性への揺るぎないコミットメントで作り上げられた、大切な思い出となります。'}
              </p>
              <div className="w-32 h-0.5 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto"></div>
            </div>
          </div>

          {/* Past Events Grid */}
          <div className="mb-16">
            <h3 className="text-3xl md:text-4xl font-serif text-gold text-center mb-12 gold-glow">
              {language === 'en' ? 'Past Events & Memories' : '過去のイベント・思い出'}
            </h3>

            {/* Responsive Grid: 1 col mobile, 2 cols tablet, 3 cols desktop */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
              {displayedEvents.map((event, index) => (
                <div
                  key={event.id}
                  id={`event-card-${event.id}`}
                  className="group cursor-pointer"
                  onClick={() => handleEventClick(event)}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {/* Portrait Card Container */}
                  <div className="relative bg-gradient-to-br from-black/90 to-gray-900/90 rounded-2xl overflow-hidden border border-gold/20 hover:border-gold/50 transition-all duration-500 hover:shadow-[0_25px_60px_rgba(212,175,55,0.4)] hover:-translate-y-3 backdrop-blur-sm h-full">

                    {/* Image Section - Square aspect ratio */}
                    <div className="relative aspect-square overflow-hidden">
                      <img
                        src={event.image}
                        alt={event.name}
                        className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                      />
                      {/* Gradient overlay for better text readability */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/30 to-transparent" />

                      {/* Floating badge */}
                      <div className="absolute top-4 right-4 bg-gold/90 backdrop-blur-sm text-black px-3 py-1 rounded-full text-xs font-bold tracking-wide">
                        {language === 'en' ? 'MEMORY' : '思い出'}
                      </div>
                    </div>

                    {/* Content Section */}
                    <div className="p-6 space-y-4">
                      {/* Event Title */}
                      <h4 className="text-xl md:text-2xl font-serif text-gold group-hover:text-gold-light transition-colors duration-300 gold-glow leading-tight line-clamp-2">
                        {language === 'en' ? event.nameEn : event.name}
                      </h4>

                      {/* Event Date */}
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-gold rounded-full"></div>
                        <p className="text-gold/90 text-sm font-medium">
                          {language === 'en' ? event.dateEn : event.date}
                        </p>
                      </div>

                      {/* Event Description */}
                      <p className="text-gold-light/80 text-sm leading-relaxed line-clamp-3">
                        {language === 'en' ? event.descriptionEn : event.description}
                      </p>

                      {/* View Details Button */}
                      <div className="pt-2">
                        <div className="inline-flex items-center gap-2 text-gold group-hover:text-gold-light transition-colors duration-300">
                          <span className="text-sm font-medium">
                            {language === 'en' ? 'View Details' : '詳細を見る'}
                          </span>
                          <svg
                            className="w-4 h-4 transition-transform duration-300 group-hover:translate-x-1"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </div>
                      </div>

                      {/* Animated underline */}
                      <div className="w-0 group-hover:w-full h-0.5 bg-gradient-to-r from-gold to-gold-light transition-all duration-700"></div>
                    </div>

                    {/* Decorative corner elements - Enhanced */}
                    <div className="absolute top-3 left-3 w-6 h-6 border-l-2 border-t-2 border-gold/40 group-hover:border-gold/80 transition-all duration-300 group-hover:scale-110"></div>
                    <div className="absolute top-3 right-3 w-6 h-6 border-r-2 border-t-2 border-gold/40 group-hover:border-gold/80 transition-all duration-300 group-hover:scale-110"></div>
                    <div className="absolute bottom-3 left-3 w-6 h-6 border-l-2 border-b-2 border-gold/40 group-hover:border-gold/80 transition-all duration-300 group-hover:scale-110"></div>
                    <div className="absolute bottom-3 right-3 w-6 h-6 border-r-2 border-b-2 border-gold/40 group-hover:border-gold/80 transition-all duration-300 group-hover:scale-110"></div>

                    {/* Subtle glow effect on hover */}
                    <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none bg-gradient-to-br from-gold/5 via-transparent to-gold/5"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Load More / View All Button */}
          {!showAll && visibleCount < pastEvents.length && (
            <div className="text-center mb-8">
              <Button
                onClick={handleLoadMore}
                className="royal-button text-black font-semibold px-8 py-3 rounded-full hover:scale-105 transition-all duration-300 shadow-[0_10px_30px_rgba(212,175,55,0.3)] hover:shadow-[0_15px_40px_rgba(212,175,55,0.4)]"
              >
                {language === 'en' ? 'Load More Memories' : 'もっと見る'}
              </Button>
            </div>
          )}


        </div>
      </section>
    </>
  );
};

export default EventsMemories;