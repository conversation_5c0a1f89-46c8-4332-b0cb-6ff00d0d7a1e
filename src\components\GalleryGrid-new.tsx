import React from "react";
import { useLanguage } from "@/hooks/use-language";
import { Camera, ZoomIn } from "lucide-react";

interface GalleryGridProps {
  images: string[];
  eventName: string;
  onImageClick: (imageUrl: string, index: number) => void;
}

const GalleryGrid: React.FC<GalleryGridProps> = ({ images, eventName, onImageClick }) => {
  const { language } = useLanguage();

  if (!images || images.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="bg-gradient-to-br from-black/90 to-gray-900/90 backdrop-blur-sm rounded-2xl p-12 border border-gold/20">
          <Camera className="w-16 h-16 text-gold/40 mx-auto mb-4" />
          <p className="text-gold-light/60 text-lg">
            {language === 'en' ? 'No images available for this event.' : 'このイベントの画像はありません。'}
          </p>
        </div>
      </div>
    );
  }

  // Determine grid layout based on number of images
  const getGridClass = (imageCount: number) => {
    if (imageCount === 1) return "grid-cols-1 max-w-2xl mx-auto";
    if (imageCount === 2) return "grid-cols-1 md:grid-cols-2";
    if (imageCount === 3) return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3";
    return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3";
  };

  return (
    <div className="space-y-8">
      {/* Section Header */}
      <div className="text-center">
        <h2 className="text-3xl md:text-4xl font-serif text-gold gold-glow mb-4">
          {language === 'en' ? 'Event Gallery' : 'イベントギャラリー'}
        </h2>
        <div className="flex items-center justify-center gap-3 text-gold-light/70">
          <Camera className="w-5 h-5" />
          <span className="text-lg">
            {language === 'en' 
              ? `${images.length} photo${images.length !== 1 ? 's' : ''} from ${eventName}`
              : `${eventName}の写真 ${images.length}枚`
            }
          </span>
        </div>
        <div className="w-24 h-0.5 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto mt-4"></div>
      </div>

      {/* Image Grid */}
      <div className={`grid ${getGridClass(images.length)} gap-6 md:gap-8`}>
        {images.map((image, index) => (
          <div
            key={index}
            className="group cursor-pointer"
            onClick={() => onImageClick(image, index)}
          >
            {/* Image Container */}
            <div className="relative aspect-square overflow-hidden rounded-2xl bg-gradient-to-br from-black/90 to-gray-900/90 border border-gold/20 hover:border-gold/50 transition-all duration-500 hover:shadow-[0_25px_60px_rgba(212,175,55,0.4)] hover:-translate-y-2">
              {/* Image */}
              <img
                src={image}
                alt={`${eventName} - Photo ${index + 1}`}
                className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                loading="lazy"
              />
              
              {/* Gradient Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              
              {/* Hover Content */}
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500">
                <div className="bg-black/70 backdrop-blur-sm rounded-full p-4 border border-gold/30 transform scale-75 group-hover:scale-100 transition-transform duration-300">
                  <ZoomIn className="w-6 h-6 text-gold" />
                </div>
              </div>
              
              {/* Image Number Badge */}
              <div className="absolute top-4 right-4 bg-gold/90 backdrop-blur-sm text-black px-3 py-1 rounded-full text-sm font-bold opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                {index + 1}
              </div>
              
              {/* Decorative corner elements */}
              <div className="absolute top-3 left-3 w-6 h-6 border-l-2 border-t-2 border-gold/40 group-hover:border-gold/80 transition-all duration-300 group-hover:scale-110"></div>
              <div className="absolute top-3 right-3 w-6 h-6 border-r-2 border-t-2 border-gold/40 group-hover:border-gold/80 transition-all duration-300 group-hover:scale-110"></div>
              <div className="absolute bottom-3 left-3 w-6 h-6 border-l-2 border-b-2 border-gold/40 group-hover:border-gold/80 transition-all duration-300 group-hover:scale-110"></div>
              <div className="absolute bottom-3 right-3 w-6 h-6 border-r-2 border-b-2 border-gold/40 group-hover:border-gold/80 transition-all duration-300 group-hover:scale-110"></div>
            </div>
          </div>
        ))}
      </div>
      
      {/* Gallery Footer */}
      <div className="text-center pt-8">
        <p className="text-gold-light/60 text-sm">
          {language === 'en' 
            ? 'Click on any image to view in full size' 
            : '画像をクリックすると大きく表示されます'
          }
        </p>
      </div>
    </div>
  );
};

export default GalleryGrid;
