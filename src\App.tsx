
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { LanguageProvider } from "@/hooks/use-language";
import { Suspense, lazy } from "react";
import MaleRecruitment from "./pages/MaleRecruitment";

// Lazy load pages for code splitting
const Index = lazy(() => import("./pages/Index"));
const AllCast = lazy(() => import("./pages/AllCast"));
const CastProfile = lazy(() => import("./pages/CastProfile"));
const StoreDetail = lazy(() => import("./pages/StoreDetail"));
const MemoryDetail = lazy(() => import("./pages/MemoryDetail"));
const NotFound = lazy(() => import("./pages/NotFound"));

// Loading component
const LoadingSpinner = () => (
  <div className="min-h-screen bg-black flex items-center justify-center">
    <div className="w-8 h-8 border-2 border-gold border-t-transparent rounded-full animate-spin"></div>
  </div>
);

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <LanguageProvider>
      <TooltipProvider>
        
        <BrowserRouter>
          <Suspense fallback={<LoadingSpinner />}>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/cast" element={<AllCast />} />
              <Route path="/cast/:id" element={<CastProfile />} />
              <Route path="/store/:id" element={<StoreDetail />} />
              <Route path="/memories/:id" element={<MemoryDetail />} />
              <Route path="/recruitment" element ={<MaleRecruitment />}/>
              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Suspense>
        </BrowserRouter>
      </TooltipProvider>
    </LanguageProvider>
  </QueryClientProvider>
);

export default App;
