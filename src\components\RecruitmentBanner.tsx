import React, { useRef } from "react";
import { useNavigate } from "react-router-dom";
import { useIntersectionObserver } from "@/hooks/use-intersection-observer";
import { ArrowRight } from "lucide-react";
import femaleBanner from '../images/banner_female.webp';
import maleBanner from '../images/banner_male.webp';

const RecruitmentBanner: React.FC = () => {
  const navigate = useNavigate();
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useIntersectionObserver(ref, { threshold: 0.1 });

  const handleRecruitmentClick = () => {
    // Navigate to recruitment page (using # for now as requested)
      window.open("https://brillar0126.com/lp-001/", "_blank");
  };
  const handleRecruitmentClickMale = () => {
    // Navigate to recruitment page (using # for now as requested)
      sessionStorage.setItem('brillar-internal-nav', 'true');
      navigate('/recruitment')
  };

  return (

      
    <section className="py-8 px-4 md:px-8 relative overflow-hidden">
      
      
      {/* Royal background elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900/50 to-black" />
      
      {/* Golden glowing title */}
      <div className="relative z-10 text-center mb-20 md:mb-36">
        <h2 className="text-2xl md:text-6xl font-serif font-bold mb-6 text-gold gold-glow">
          ようこそ、非日常の楽園へ。洗練と癒しが織りなす、極上のひとときを貴方に。
        </h2>
        <div className="w-32 h-1 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto mb-8"></div>
      </div>
      
      <div 
        ref={ref}
        className={`max-w-7xl mx-auto reveal relative z-10 space-y-6 ${isInView ? 'active' : ''}`}
      >
        {/* Female Cast Recruitment Banner */}
        <div 
          className="group cursor-pointer relative rounded-xl overflow-hidden border border-gold/20 hover:border-gold/40 transition-all duration-500 hover:shadow-[0_20px_50px_rgba(212,175,55,0.3)] hover:-translate-y-1"
          onClick={handleRecruitmentClick}
        >
          <div className="relative w-full aspect-[4/3] sm:aspect-[16/9] md:aspect-[20/9] lg:aspect-[24/9] overflow-hidden">
            <img 
              src={femaleBanner}
              alt="Female Cast Recruitment - Join our elite team"
              className="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-105"
            />
            

            {/* Animated Button y at bottom right */}
            <div className="absolute bottom-4 right-4 md:bottom-6 md:right-6">
              <a href="https://brillar0126.com/lp-001/">
              <button className="group/btn bg-gold hover:bg-yellow-400 text-black px-4 py-2 md:px-6 md:py-3 rounded-full font-bold text-sm md:text-base transition-all duration-300 transform hover:scale-105 hover:shadow-lg flex items-center gap-2 shadow-lg border border-black/10" >
                <span>Apply Now</span>
                <ArrowRight className="w-4 h-4 md:w-5 md:h-5 transition-transform duration-300 group-hover/btn:translate-x-1" />
              </button>
              </a>
            </div>
            
            {/* Decorative corner elements */}
            <div className="absolute top-4 left-4 w-6 h-6 border-l-2 border-t-2 border-gold/40 group-hover:border-gold/70 transition-colors duration-300"></div>
            <div className="absolute top-4 right-4 w-6 h-6 border-r-2 border-t-2 border-gold/40 group-hover:border-gold/70 transition-colors duration-300"></div>
            <div className="absolute bottom-4 left-4 w-6 h-6 border-l-2 border-b-2 border-gold/40 group-hover:border-gold/70 transition-colors duration-300"></div>
          </div>
        </div>

        {/* Male Staff Recruitment Banner */}
        <div 
          className="group cursor-pointer relative rounded-xl overflow-hidden border border-gold/20 hover:border-gold/40 transition-all duration-500 hover:shadow-[0_20px_50px_rgba(212,175,55,0.3)] hover:-translate-y-1"
          onClick={handleRecruitmentClickMale}
        >
          <div className="relative w-full aspect-[16/10] md:aspect-[16/7] lg:aspect-[18/7] overflow-hidden">
            <img 
              src={maleBanner}
              alt="Male Staff Recruitment - Join our professional team"
              className="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-105"
            />
            
            {/* Animated Button at bottom right */}
            <div className="absolute bottom-4 right-4 md:bottom-6 md:right-6">
              <button className="group/btn bg-gold hover:bg-yellow-400 text-black px-4 py-2 md:px-6 md:py-3 rounded-full font-bold text-sm md:text-base transition-all duration-300 transform hover:scale-105 hover:shadow-lg flex items-center gap-2 shadow-lg border border-black/10">
                <span>Apply Now</span>
                <ArrowRight className="w-4 h-4 md:w-5 md:h-5 transition-transform duration-300 group-hover/btn:translate-x-1" />
              </button>
            </div>
            
            {/* Decorative corner elements */}
            <div className="absolute top-4 left-4 w-6 h-6 border-l-2 border-t-2 border-gold/40 group-hover:border-gold/70 transition-colors duration-300"></div>
            <div className="absolute top-4 right-4 w-6 h-6 border-r-2 border-t-2 border-gold/40 group-hover:border-gold/70 transition-colors duration-300"></div>
            <div className="absolute bottom-4 left-4 w-6 h-6 border-l-2 border-b-2 border-gold/40 group-hover:border-gold/70 transition-colors duration-300"></div>
          </div>
        </div>
      </div>
    </section>

  );
};

export default RecruitmentBanner;
