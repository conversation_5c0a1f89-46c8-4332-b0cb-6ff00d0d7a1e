import React from "react";
import { useNavigate } from "react-router-dom";
import { useLanguage } from "@/hooks/use-language";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Calendar, Tag } from "lucide-react";

interface MemoryDetailHeaderProps {
  memory: {
    id: number;
    name: string;
    nameEn: string;
    date: string;
    dateEn: string;
    image: string;
    description: string;
    descriptionEn: string;
    longDescription: string;
    longDescriptionEn: string;
    tags: string[];
    tagsEn: string[];
  };
}

const MemoryDetailHeader: React.FC<MemoryDetailHeaderProps> = ({ memory }) => {
  const { language } = useLanguage();
  const navigate = useNavigate();

  const handleBackClick = () => {
    const returnSource = sessionStorage.getItem('brillar-return-source');
    const eventId = sessionStorage.getItem('brillar-return-event-id');

    // Clear the return source and event ID after reading
    sessionStorage.removeItem('brillar-return-source');
    sessionStorage.removeItem('brillar-return-event-id');
    sessionStorage.setItem('brillar-internal-nav', 'true');

    if (returnSource === 'home-events-memories') {
      // Set target section for home page to scroll to specific event card
      if (eventId) {
        sessionStorage.setItem('brillar-return-target', `event-card-${eventId}`);
      } else {
        sessionStorage.setItem('brillar-return-target', 'past-events-memories');
      }
      navigate('/');
    } else {
      // Fallback: just go to home page
      navigate('/');
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-12">
      {/* Back Button */}
      <div className="flex justify-start">
        <Button
          onClick={handleBackClick}
          variant="outline"
          className="bg-black/50 border-gold/30 text-gold hover:bg-gold/10 hover:border-gold/50 transition-all duration-300"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          {language === 'en' ? 'Back to Memories' : '思い出に戻る'}
        </Button>
      </div>

      {/* Title Section */}
      <div className="text-center space-y-6">
        <h1 className="text-4xl md:text-6xl lg:text-7xl font-serif text-gold gold-glow leading-tight">
          {language === 'en' ? memory.nameEn : memory.name}
        </h1>
        
        <div className="flex items-center justify-center gap-3 text-gold-light">
          <Calendar className="w-5 h-5" />
          <span className="text-lg md:text-xl font-medium">
            {language === 'en' ? memory.dateEn : memory.date}
          </span>
        </div>

        <div className="w-24 h-0.5 bg-gradient-to-r from-transparent via-gold to-transparent mx-auto"></div>
      </div>

      {/* Event Description */}
      <div className="space-y-8 text-center">
        <div className="max-w-3xl mx-auto">
          <p className="text-gold-light text-lg md:text-xl leading-relaxed">
            {language === 'en' ? memory.descriptionEn : memory.description}
          </p>
        </div>

        {/* About This Event */}
        <div className="max-w-3xl mx-auto space-y-4">
          <h2 className="text-2xl md:text-3xl font-serif text-gold gold-glow">
            {language === 'en' ? 'About This Event' : 'このイベントについて'}
          </h2>
          <p className="text-gold-light/90 text-base md:text-lg leading-relaxed">
            {language === 'en' ? memory.longDescriptionEn : memory.longDescription}
          </p>
        </div>

        {/* Tags */}
        <div className="flex items-center justify-center gap-2 flex-wrap">
          <Tag className="w-4 h-4 text-gold/80" />
          {(language === 'en' ? memory.tagsEn : memory.tags).map((tag, index) => (
            <span
              key={index}
              className="px-4 py-2 bg-black/30 backdrop-blur-sm text-gold text-sm rounded-full border border-gold/30 hover:border-gold/50 transition-colors duration-300"
            >
              {tag}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
};
export default MemoryDetailHeader;
