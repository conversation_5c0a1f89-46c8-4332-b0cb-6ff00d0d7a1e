// Mock memory data for the memories detail page
import event1 from '../images/event1.webp';
import event1s1 from '../images/event1-s1.webp';
import event1s2 from '../images/event1-s2.webp';
import event2 from '../images/event2.webp';
import event3 from '../images/event3.webp';
import featureImg from '../images/feature-event.webp';

export const memoriesData = [
  {
    id: 4,
    name: "夜店の屋台始めました",
    nameEn: "Golden Night Gala",
    date: "2025年7月20日",
    dateEn: "July 20, 2025",
    image: featureImg,
    description: "夏だ！！祭りだ！！ブリジャールだ！！２本 きゅうりの一本漬け からあげ たこ焼き フランクフルト ALL ¥1,000",
    descriptionEn: "The most luxurious gala event of the year featuring special guests and premium entertainment",
    longDescription: "夏の特別な夜に開催された屋台イベント。きゅうりの一本漬け、からあげ、たこ焼き、フランクフルトなど、様々な美味しい食べ物を提供しました。お客様には大変ご好評をいただき、素晴らしい夜となりました。",
    longDescriptionEn: "A special summer night featuring our street food stall event. We offered various delicious foods including pickled cucumber, fried chicken, takoyaki, and frankfurters. Our customers greatly enjoyed the evening, making it a wonderful night for everyone.",
    gallery: [featureImg],
    tags: ["夏祭り", "屋台", "フード"],
    tagsEn: ["Summer Festival", "Street Food", "Food"]
  },
  {
    id: 1,
    name: "まな生誕祭",
    nameEn: "Mana Birthday Festival",
    date: "令和七年二月八日（土）",
    dateEn: "Saturday, February 8, 2025",
    image: event1,
    description: "まなの誕生日を祝う華やかなイベント",
    descriptionEn: "A glamorous event celebrating Mana's birthday",
    longDescription: "まなの特別な誕生日を祝うため、店内を華やかに装飾し、特別なパフォーマンスとサプライズを用意しました。お客様と一緒に素敵な思い出を作ることができました。",
    longDescriptionEn: "To celebrate Mana's special birthday, we decorated the venue glamorously and prepared special performances and surprises. We were able to create wonderful memories together with our customers.",
    gallery: [event1, event1s1, event1s2],
    tags: ["誕生日", "パーティー", "セレブレーション"],
    tagsEn: ["Birthday", "Party", "Celebration"]
  },
  {
    id: 2,
    name: "セクシーハロウィン",
    nameEn: "Sexy Halloween",
    date: "Lucia: 10月18日(金)・19日(土), BRILLAR: 10月25日(金)・26日(土)",
    dateEn: "Lucia: October 18 (Fri)–19 (Sat), BRILLAR: October 25 (Fri)–26 (Sat)",
    image: event2,
    description: "ルシアとブリリャーで開催されるセクシーなハロウィンイベント",
    descriptionEn: "A sexy Halloween event held at Lucia and Brillar",
    longDescription: "ハロウィンシーズンに開催された特別なイベント。ルシアとブリリャーの両店舗で、セクシーで魅力的なコスチュームを着用したスタッフがお客様をお迎えしました。",
    longDescriptionEn: "A special event held during Halloween season. At both Lucia and Brillar venues, our staff welcomed customers wearing sexy and attractive costumes.",
    gallery: [event2],
    tags: ["ハロウィン", "コスチューム", "イベント"],
    tagsEn: ["Halloween", "Costume", "Event"]
  },
  {
    id: 3,
    name: "26周年バースデー",
    nameEn: "26th Anniversary Birthday",
    date: "日付不明",
    dateEn: "Date unknown",
    image: event3,
    description: "シンデレラ風のテーマで祝う26周年の特別な誕生日イベント",
    descriptionEn: "A special 26th birthday event with a Cinderella-style theme",
    longDescription: "シンデレラをテーマにした特別な26周年記念イベント。魔法のような夜を演出し、お客様に特別な体験を提供しました。",
    longDescriptionEn: "A special 26th anniversary event with a Cinderella theme. We created a magical night and provided our customers with a special experience.",
    gallery: [event3],
    tags: ["記念日", "シンデレラ", "特別イベント"],
    tagsEn: ["Anniversary", "Cinderella", "Special Event"]
  }
];

export const getMemoryById = (id) => {
  return memoriesData.find(memory => memory.id === parseInt(id));
};

export const getAllMemories = () => {
  return memoriesData;
};
