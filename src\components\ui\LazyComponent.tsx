import React, { useState, useRef } from 'react';
import { useIntersectionObserver } from '@/hooks/use-intersection-observer';

interface LazyComponentProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  threshold?: number;
  rootMargin?: string;
  className?: string;
  style?: React.CSSProperties;
  minHeight?: string;
}

const LazyComponent: React.FC<LazyComponentProps> = ({
  children,
  fallback = <div className="w-full h-32 bg-gray-800 animate-pulse rounded" />,
  threshold = 0.1,
  rootMargin = '100px',
  className = '',
  style,
  minHeight = 'auto'
}) => {
  const [hasLoaded, setHasLoaded] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const isInView = useIntersectionObserver(containerRef, {
    threshold,
    rootMargin
  });

  React.useEffect(() => {
    if (isInView && !hasLoaded) {
      setHasLoaded(true);
    }
  }, [isInView, hasLoaded]);

  const containerStyle: React.CSSProperties = {
    minHeight,
    contentVisibility: 'auto',
    containIntrinsicSize: minHeight !== 'auto' ? `auto ${minHeight}` : 'auto 200px',
    ...style
  };

  return (
    <div
      ref={containerRef}
      className={`lazy-component ${className}`}
      style={containerStyle}
    >
      {hasLoaded ? children : fallback}
    </div>
  );
};

export default LazyComponent;
