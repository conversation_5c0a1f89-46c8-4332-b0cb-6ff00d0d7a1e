import React from 'react';

// Lazy load all major components
const Hero = React.lazy(() => import('./Hero'));
const About = React.lazy(() => import('./About'));
const Gallery = React.lazy(() => import('./Gallery'));
const ShopOverview = React.lazy(() => import('./ShopOverview'));
const EventsMemories = React.lazy(() => import('./EventsMemories'));
const News = React.lazy(() => import('./News'));
const NoticeBoard = React.lazy(() => import('./NoticeBoard'));
const Access = React.lazy(() => import('./Access'));
const SocialMedia = React.lazy(() => import('./SocialMedia'));
const Footer = React.lazy(() => import('./Footer'));

// Performance-optimized loading fallbacks
const ComponentSkeleton = ({ height = '400px', className = '' }: { height?: string; className?: string }) => (
  <div 
    className={`w-full bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 animate-pulse ${className}`}
    style={{ height, contentVisibility: 'auto' }}
  >
    <div className="flex items-center justify-center h-full">
      <div className="text-gold/50 text-sm">Loading...</div>
    </div>
  </div>
);

const HeroSkeleton = () => (
  <ComponentSkeleton height="100vh" className="bg-gradient-to-br from-black via-gray-900 to-black" />
);

const SectionSkeleton = () => (
  <ComponentSkeleton height="60vh" className="rounded-lg mx-4 my-8" />
);

const GallerySkeleton = () => (
  <div className="w-full py-20 bg-black">
    <div className="max-w-7xl mx-auto px-4">
      <div className="h-8 bg-gray-700 rounded w-64 mx-auto mb-8 animate-pulse" />
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {Array.from({ length: 8 }).map((_, i) => (
          <div key={i} className="aspect-square bg-gray-800 rounded-lg animate-pulse" />
        ))}
      </div>
    </div>
  </div>
);

const FooterSkeleton = () => (
  <ComponentSkeleton height="300px" className="bg-black mt-auto" />
);

export {
  Hero,
  About,
  Gallery,
  ShopOverview,
  EventsMemories,
  News,
  NoticeBoard,
  Access,
  SocialMedia,
  Footer,
  HeroSkeleton,
  SectionSkeleton,
  GallerySkeleton,
  FooterSkeleton
};
